#!/usr/bin/env node
import WebSocket from 'ws';
import { spawn } from 'child_process';

// 连接到Windows控制端的WebSocket服务器
const WS_SERVER_URI = 'ws://localhost:8765';

let reconnectAttempts = 0;
const maxReconnectAttempts = 10;
let reconnectInterval = 5000; // 5秒

// 存储当前运行的推流进程
let streamProcess = null;

function connectToServer() {
    console.log(`🔗 正在连接到 ${WS_SERVER_URI} (尝试 ${reconnectAttempts + 1})`);
    
    const ws = new WebSocket(WS_SERVER_URI);
    
    ws.on('open', () => {
        console.log('✅ 已连接到 Windows 控制端');
        reconnectAttempts = 0; // 重置重连计数
        
        // 发送初始状态
        sendStatus(ws, '客户端已连接，等待命令');
    });
    
    ws.on('message', (data) => {
        const cmd = data.toString().trim();
        console.log(`📨 收到命令: ${cmd}`);
        
        handleCommand(ws, cmd);
    });
    
    ws.on('close', (code, reason) => {
        console.log(`🔌 连接关闭: ${code} ${reason}`);
        
        if (reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            console.log(`⏳ ${reconnectInterval / 1000}秒后重试连接...`);
            setTimeout(connectToServer, reconnectInterval);
        } else {
            console.log('❌ 达到最大重连次数，停止重连');
            process.exit(1);
        }
    });
    
    ws.on('error', (error) => {
        console.error(`❌ 连接失败: ${error.message}`);
        
        if (reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            console.log(`⏳ ${reconnectInterval / 1000}秒后重试连接...`);
            setTimeout(connectToServer, reconnectInterval);
        } else {
            console.log('❌ 达到最大重连次数，停止重连');
            process.exit(1);
        }
    });
    
    // 处理ping
    ws.on('ping', () => {
        ws.pong();
    });
    
    return ws;
}

function sendStatus(ws, status) {
    if (ws.readyState === 1) {
        try {
            ws.send(status);
            console.log(`📤 状态已发送: ${status}`);
        } catch (error) {
            console.error(`❌ 发送状态失败: ${error.message}`);
        }
    }
}

function handleCommand(ws, cmd) {
    switch (cmd) {
        case 'start_stream':
            startStream(ws);
            break;
            
        case 'stop_stream':
            stopStream(ws);
            break;
            
        case 'status':
            getStatus(ws);
            break;
            
        case 'server_shutdown':
            console.log('🛑 服务器即将关闭，客户端退出');
            process.exit(0);
            break;
            
        case 'exit':
            sendStatus(ws, '客户端退出');
            setTimeout(() => process.exit(0), 1000);
            break;
            
        default:
            console.log(`⚠️  未知命令: ${cmd}`);
            sendStatus(ws, `未知命令: ${cmd}`);
    }
}

function startStream(ws) {
    if (streamProcess && !streamProcess.killed) {
        sendStatus(ws, '推流已在运行中');
        return;
    }
    
    try {
        // 启动推流进程 (这里使用python3作为示例，你可以根据实际情况修改)
        streamProcess = spawn('python3', ['pushstream.py'], {
            stdio: 'pipe',
            detached: false
        });
        
        streamProcess.on('spawn', () => {
            console.log('🎥 推流进程已启动');
            sendStatus(ws, '推流已启动');
        });
        
        streamProcess.on('error', (error) => {
            console.error(`❌ 启动推流失败: ${error.message}`);
            sendStatus(ws, `启动推流失败: ${error.message}`);
            streamProcess = null;
        });
        
        streamProcess.on('exit', (code, signal) => {
            console.log(`🎥 推流进程退出: code=${code}, signal=${signal}`);
            sendStatus(ws, `推流进程退出: ${code}`);
            streamProcess = null;
        });
        
        // 处理推流进程的输出
        streamProcess.stdout.on('data', (data) => {
            console.log(`📺 推流输出: ${data.toString().trim()}`);
        });
        
        streamProcess.stderr.on('data', (data) => {
            console.error(`📺 推流错误: ${data.toString().trim()}`);
        });
        
    } catch (error) {
        console.error(`❌ 启动推流异常: ${error.message}`);
        sendStatus(ws, `启动推流异常: ${error.message}`);
    }
}

function stopStream(ws) {
    if (!streamProcess || streamProcess.killed) {
        sendStatus(ws, '推流未在运行');
        return;
    }
    
    try {
        // 优雅地终止进程
        streamProcess.kill('SIGTERM');
        
        // 如果5秒后进程还没退出，强制杀死
        setTimeout(() => {
            if (streamProcess && !streamProcess.killed) {
                console.log('🔪 强制终止推流进程');
                streamProcess.kill('SIGKILL');
            }
        }, 5000);
        
        console.log('🛑 推流停止命令已发送');
        sendStatus(ws, '推流已停止');
        
    } catch (error) {
        console.error(`❌ 停止推流失败: ${error.message}`);
        sendStatus(ws, `停止推流失败: ${error.message}`);
    }
}

function getStatus(ws) {
    let status;
    
    if (streamProcess && !streamProcess.killed) {
        status = '推流正在运行中';
    } else {
        status = '等待推流命令';
    }
    
    console.log(`📊 当前状态: ${status}`);
    sendStatus(ws, status);
}

// 处理进程退出
process.on('SIGINT', () => {
    console.log('\n🛑 收到中断信号，正在清理...');
    
    if (streamProcess && !streamProcess.killed) {
        console.log('🛑 停止推流进程...');
        streamProcess.kill('SIGTERM');
    }
    
    setTimeout(() => {
        process.exit(0);
    }, 2000);
});

process.on('SIGTERM', () => {
    console.log('🛑 收到终止信号，正在清理...');
    
    if (streamProcess && !streamProcess.killed) {
        streamProcess.kill('SIGTERM');
    }
    
    process.exit(0);
});

// 启动客户端
console.log('🚀 WebSocket客户端启动');
connectToServer();
