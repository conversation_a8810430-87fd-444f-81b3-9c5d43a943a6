#!/usr/bin/env node
import WebSocket from 'ws';
import { spawn } from 'child_process';

// 连接到Windows控制端的WebSocket服务器
const WS_SERVER_URI = 'ws://************:8766';

let reconnectAttempts = 0;
const maxReconnectAttempts = 10;
let reconnectInterval = 5000; // 5秒

// 存储当前运行的推流进程
let streamProcess = null;

// 检查是否为有效命令
function isValidCommand(message) {
    const validCommands = ['start_stream', 'stop_stream', 'status', 'exit', 'server_shutdown'];
    return validCommands.includes(message) || message.startsWith('custom_cmd:');
}

function connectToServer() {
    console.log(`🔗 正在连接到 ${WS_SERVER_URI} (尝试 ${reconnectAttempts + 1})`);

    const ws = new WebSocket(WS_SERVER_URI);

    ws.on('open', () => {
        console.log('✅ 已连接到 Windows 控制端');
        reconnectAttempts = 0; // 重置重连计数

        // 发送初始状态
        sendStatus(ws, '客户端已连接，等待命令');
    });

    ws.on('message', (data) => {
        const message = data.toString().trim();
        console.log(`📨 收到消息: ${message}`);
        console.log('debug => ', message)
        // 只处理真正的命令，忽略状态确认消息
        if (isValidCommand(message)) {
            console.log('in => ', message)
            console.log(`🎯 处理命令: ${message}`);
            handleCommand(ws, message);
        } else {
            console.log(`ℹ️  忽略非命令消息: ${message}`);
        }
    });

    ws.on('close', (code, reason) => {
        console.log(`🔌 连接关闭: ${code} ${reason}`);

        if (reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            console.log(`⏳ ${reconnectInterval / 1000}秒后重试连接...`);
            setTimeout(connectToServer, reconnectInterval);
        } else {
            console.log('❌ 达到最大重连次数，停止重连');
            process.exit(1);
        }
    });

    ws.on('error', (error) => {
        console.error(`❌ 连接失败: ${error.message}`);

        if (reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            console.log(`⏳ ${reconnectInterval / 1000}秒后重试连接...`);
            setTimeout(connectToServer, reconnectInterval);
        } else {
            console.log('❌ 达到最大重连次数，停止重连');
            process.exit(1);
        }
    });

    // 处理ping
    ws.on('ping', () => {
        ws.pong();
    });

    return ws;
}

function sendStatus(ws, status) {
    if (ws.readyState === 1) {
        try {
            ws.send(status);
            console.log(`📤 状态已发送: ${status}`);
        } catch (error) {
            console.error(`❌ 发送状态失败: ${error.message}`);
        }
    }
}

function handleCommand(ws, cmd) {
    console.log(`📨 收到指令: ${cmd}`);

    if (cmd.startsWith('custom_cmd:')) {
        // 处理自定义命令
        const actualCmd = cmd.substring('custom_cmd:'.length);
        console.log(`🎯 执行自定义命令: ${actualCmd}`);
        executeCustomCommand(ws, actualCmd);
        return;
    }

    switch (cmd) {
        case 'start_stream':
            console.log('🎥 执行启动推流指令');
            startStream(ws);
            break;

        case 'stop_stream':
            console.log('🛑 执行停止推流指令');
            stopStream(ws);
            break;

        case 'status':
            console.log('📊 执行状态查询指令');
            getStatus(ws);
            break;

        case 'server_shutdown':
            console.log('🛑 服务器即将关闭，客户端退出');
            sendStatus(ws, '已执行指令: 客户端退出');
            process.exit(0);

        case 'exit':
            console.log('👋 执行退出指令');
            sendStatus(ws, '已执行指令: 客户端退出');
            setTimeout(() => process.exit(0), 1000);
            break;

        default:
            console.log(`⚠️  未知命令: ${cmd}`);
            sendStatus(ws, `未知命令: ${cmd}`);
    }
}

function startStream(ws) {
    if (streamProcess && !streamProcess.killed) {
        sendStatus(ws, '推流已在运行中');
        return;
    }

    try {
        // 启动推流进程 (这里使用python3作为示例，你可以根据实际情况修改)
        streamProcess = spawn("/usr/bin/rpicam-vid", [
            "--width", "1280",
            "--height", "720",
            "--framerate", "30",
            "--codec", "h264",
            "--inline",
            "--timeout", "0",
            "--libav-format", "flv",
            "--output", "rtmp://************:1935/live/device_001"
        ], {
            stdio: ["ignore", "pipe", "pipe"], // 忽略 stdin，捕获 stdout 和 stderr
            detached: false
        });

        streamProcess.on('spawn', () => {
            console.log('✅ 推流进程已启动');
            sendStatus(ws, '已执行指令: 推流已启动');
        });

        streamProcess.on('error', (error) => {
            console.error(`❌ 启动推流失败: ${error.message}`);
            sendStatus(ws, `已执行指令: 启动推流失败 - ${error.message}`);
            streamProcess = null;
        });

        streamProcess.on('exit', (code, signal) => {
            console.log(`🎥 推流进程退出: code=${code}, signal=${signal}`);
            sendStatus(ws, `推流进程退出: ${code}`);
            streamProcess = null;
        });

        // 静默处理推流进程的输出，避免干扰命令输入
        streamProcess.stdout.on('data', (data) => {
            // 可以选择性地记录重要信息，但不输出到控制台
            // console.log(`📺 推流输出: ${data.toString().trim()}`);
        });

        streamProcess.stderr.on('data', (data) => {
            // 只记录真正的错误，忽略正常的状态信息
            const output = data.toString().trim();
            if (output.includes('error') || output.includes('Error') || output.includes('failed')) {
                console.error(`📺 推流错误: ${output}`);
            }
            // 其他输出（如fps信息）被静默处理
        });

    } catch (error) {
        console.error(`❌ 启动推流异常: ${error.message}`);
        sendStatus(ws, `启动推流异常: ${error.message}`);
    }
}

function stopStream(ws) {
    if (!streamProcess || streamProcess.killed) {
        console.log('⚠️  推流未在运行');
        sendStatus(ws, '已执行指令: 推流未在运行');
        return;
    }

    try {
        // 优雅地终止进程
        streamProcess.kill('SIGTERM');

        // 如果5秒后进程还没退出，强制杀死
        setTimeout(() => {
            if (streamProcess && !streamProcess.killed) {
                console.log('🔪 强制终止推流进程');
                streamProcess.kill('SIGKILL');
            }
        }, 5000);

        console.log('✅ 推流停止命令已发送');
        sendStatus(ws, '已执行指令: 推流已停止');

    } catch (error) {
        console.error(`❌ 停止推流失败: ${error.message}`);
        sendStatus(ws, `已执行指令: 停止推流失败 - ${error.message}`);
    }
}

function getStatus(ws) {
    let status;

    if (streamProcess && !streamProcess.killed) {
        status = '推流正在运行中';
    } else {
        status = '等待推流命令';
    }

    console.log(`📊 当前状态: ${status}`);
    sendStatus(ws, `已执行指令: 状态查询 - ${status}`);
}

function executeCustomCommand(ws, command) {
    console.log(`🔧 执行自定义命令: ${command}`);

    // 如果有正在运行的推流，先停止它
    if (streamProcess && !streamProcess.killed) {
        console.log('🛑 停止当前推流进程...');
        streamProcess.kill('SIGTERM');
    }

    try {
        // 解析命令参数
        const args = command.split(' ');
        const executable = args[0];
        const params = args.slice(1);

        console.log(`🚀 启动命令: ${executable} ${params.join(' ')}`);

        // 启动自定义命令
        streamProcess = spawn(executable, params, {
            stdio: ['ignore', 'pipe', 'pipe'], // 忽略stdin，捕获stdout和stderr
            detached: false
        });

        streamProcess.on('spawn', () => {
            console.log('✅ 自定义命令已启动');
            sendStatus(ws, `已执行指令: 自定义命令已启动 - ${executable}`);
        });

        streamProcess.on('error', (error) => {
            console.error(`❌ 执行自定义命令失败: ${error.message}`);
            sendStatus(ws, `已执行指令: 执行命令失败 - ${error.message}`);
            streamProcess = null;
        });

        streamProcess.on('exit', (code, signal) => {
            console.log(`🏁 自定义命令退出: code=${code}, signal=${signal}`);
            sendStatus(ws, `已执行指令: 命令执行完成 - 退出码${code}`);
            streamProcess = null;
        });

        // 静默处理命令输出，避免干扰
        streamProcess.stdout.on('data', () => {
            // 静默处理stdout输出
        });

        streamProcess.stderr.on('data', (data) => {
            // 只记录真正的错误，忽略正常的状态信息（如fps）
            const output = data.toString().trim();
            if (output.includes('error') || output.includes('Error') || output.includes('failed') || output.includes('Fatal')) {
                console.error(`📺 命令错误: ${output}`);
            }
            // fps和其他状态信息被静默处理
        });

    } catch (error) {
        console.error(`❌ 执行自定义命令异常: ${error.message}`);
        sendStatus(ws, `已执行指令: 执行命令异常 - ${error.message}`);
    }
}

// 处理进程退出
process.on('SIGINT', () => {
    console.log('\n🛑 收到中断信号，正在清理...');

    if (streamProcess && !streamProcess.killed) {
        console.log('🛑 停止推流进程...');
        streamProcess.kill('SIGTERM');
    }

    setTimeout(() => {
        process.exit(0);
    }, 2000);
});

process.on('SIGTERM', () => {
    console.log('🛑 收到终止信号，正在清理...');

    if (streamProcess && !streamProcess.killed) {
        streamProcess.kill('SIGTERM');
    }

    process.exit(0);
});

// 启动客户端
console.log('🚀 WebSocket客户端启动');
connectToServer();
