#!/usr/bin/env python3
import asyncio
import websockets
import logging

# 启用详细日志来调试问题
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

clients = set()

async def handler(websocket, path):
    """WebSocket连接处理器 - 注意需要两个参数！"""
    client_addr = websocket.remote_address
    logger.info(f"新连接建立: {client_addr}, path: {path}")
    
    try:
        # 添加到客户端列表
        clients.add(websocket)
        logger.info(f"客户端已添加，当前连接数: {len(clients)}")
        
        # 发送欢迎消息
        try:
            await websocket.send("连接成功，等待命令...")
            logger.info(f"欢迎消息已发送到 {client_addr}")
        except Exception as e:
            logger.error(f"发送欢迎消息失败: {e}")
            return
        
        # 监听客户端消息
        async for message in websocket:
            logger.info(f"收到来自 {client_addr} 的消息: {message}")
            
            try:
                # 简单回应确认收到
                await websocket.send(f"收到状态: {message}")
                logger.info(f"状态确认已发送到 {client_addr}")
            except Exception as e:
                logger.error(f"发送状态确认失败: {e}")
                break
                
    except websockets.exceptions.ConnectionClosed as e:
        logger.info(f"连接 {client_addr} 正常关闭: {e}")
    except Exception as e:
        logger.error(f"处理连接 {client_addr} 时发生错误: {e}")
        logger.exception("详细错误信息:")
    finally:
        # 清理连接
        try:
            clients.discard(websocket)
            logger.info(f"连接 {client_addr} 已清理，当前连接数: {len(clients)}")
        except Exception as e:
            logger.error(f"清理连接时出错: {e}")

async def send_command(cmd):
    """向所有客户端发送命令"""
    if not clients:
        logger.warning("没有活跃的客户端连接")
        return
    
    logger.info(f"准备向 {len(clients)} 个客户端发送命令: {cmd}")
    
    # 记录断开的连接
    disconnected = set()
    
    for client in clients.copy():
        try:
            await client.send(cmd)
            logger.info(f"命令已发送到客户端: {client.remote_address}")
        except websockets.exceptions.ConnectionClosed:
            logger.warning(f"客户端 {client.remote_address} 连接已关闭")
            disconnected.add(client)
        except Exception as e:
            logger.error(f"发送命令到 {client.remote_address} 失败: {e}")
            disconnected.add(client)
    
    # 清理断开的连接
    if disconnected:
        clients -= disconnected
        logger.info(f"已清理 {len(disconnected)} 个断开的连接")

async def command_input():
    """处理用户命令输入"""
    logger.info("命令输入协程已启动")
    
    while True:
        try:
            cmd = await asyncio.get_event_loop().run_in_executor(
                None, input, "输入命令 (start_stream/stop_stream/status/exit): "
            )
            
            cmd = cmd.strip()
            logger.info(f"用户输入命令: {cmd}")
            
            if cmd == "exit":
                logger.info("收到退出命令")
                break
            elif cmd in ["start_stream", "stop_stream", "status"]:
                await send_command(cmd)
            else:
                logger.warning(f"未知命令: {cmd}")
                
        except KeyboardInterrupt:
            logger.info("收到键盘中断")
            break
        except Exception as e:
            logger.error(f"命令输入处理错误: {e}")

async def main():
    """主函数"""
    host = "0.0.0.0"  # 监听所有接口
    port = 8765
    
    logger.info(f"正在启动WebSocket服务器...")
    logger.info(f"绑定地址: {host}:{port}")
    
    try:
        # 启动WebSocket服务器
        server = await websockets.serve(
            handler,
            host,
            port,
            ping_interval=20,  # 20秒ping间隔
            ping_timeout=10,   # 10秒ping超时
            close_timeout=10   # 10秒关闭超时
        )
        
        logger.info("✅ WebSocket服务器启动成功!")
        logger.info(f"监听地址: ws://{host}:{port}")
        logger.info("等待客户端连接...")
        
        # 启动命令输入协程
        await command_input()
        
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        logger.exception("详细错误:")
    finally:
        logger.info("正在关闭服务器...")
        try:
            server.close()
            await server.wait_closed()
            logger.info("服务器已关闭")
        except Exception as e:
            logger.error(f"关闭服务器时出错: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
