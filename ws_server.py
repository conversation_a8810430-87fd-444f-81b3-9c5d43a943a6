#!/usr/bin/env python3
import asyncio
import websockets

clients = set()

async def handler(ws):
    clients.add(ws)
    print("树莓派已连接")
    try:
        async for message in ws:
            print("树莓派状态:", message)
    finally:
        clients.remove(ws)
        print("树莓派断开连接")

async def send_command(cmd):
    if clients:
        await asyncio.wait([c.send(cmd) for c in clients])

async def main():
    server = await websockets.serve(handler, "0.0.0.0", 8765)
    print("WebSocket 服务已启动，等待树莓派连接...")

    while True:
        cmd = input("输入命令 (start_stream/stop_stream/status/exit): ")
        await send_command(cmd)
        if cmd == "exit":
            break

asyncio.run(main())
