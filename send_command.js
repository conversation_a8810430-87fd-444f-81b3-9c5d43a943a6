#!/usr/bin/env node
import WebSocket from 'ws';
import { createInterface } from 'readline';

const WS_SERVER_URI = 'ws://localhost:8766';

// 创建命令行接口
const rl = createInterface({
    input: process.stdin,
    output: process.stdout
});

function connectAndSendCommand() {
    console.log('🔗 连接到WebSocket服务器...');
    
    const ws = new WebSocket(WS_SERVER_URI);
    
    ws.on('open', () => {
        console.log('✅ 已连接到服务器');
        console.log('💬 你现在可以输入命令了');
        promptCommand(ws);
    });
    
    ws.on('message', (data) => {
        const message = data.toString();
        console.log(`📨 服务器响应: ${message}`);
    });
    
    ws.on('close', (code, reason) => {
        console.log(`🔌 连接关闭: ${code} ${reason || '正常关闭'}`);
        rl.close();
        process.exit(0);
    });
    
    ws.on('error', (error) => {
        console.error(`❌ 连接错误: ${error.message}`);
        console.log('请确保WebSocket服务器正在运行');
        rl.close();
        process.exit(1);
    });
}

function promptCommand(ws) {
    rl.question('\n输入命令 (start_stream/stop_stream/status/exit/或自定义命令): ', (input) => {
        const command = input.trim();
        
        if (command === 'exit') {
            console.log('🛑 发送退出命令...');
            ws.send('exit');
            setTimeout(() => {
                ws.close();
            }, 1000);
            return;
        }
        
        if (command === '') {
            console.log('⚠️  空命令，请重新输入');
            promptCommand(ws);
            return;
        }
        
        // 发送命令
        console.log(`📤 发送命令: ${command}`);
        
        if (['start_stream', 'stop_stream', 'status'].includes(command)) {
            ws.send(command);
        } else {
            // 自定义命令，添加前缀
            ws.send(`custom_cmd:${command}`);
        }
        
        // 继续等待下一个命令
        promptCommand(ws);
    });
}

// 处理进程退出
process.on('SIGINT', () => {
    console.log('\n🛑 退出命令工具');
    rl.close();
    process.exit(0);
});

console.log('🚀 WebSocket命令发送工具');
console.log('📍 目标服务器: ' + WS_SERVER_URI);
console.log('');
connectAndSendCommand();
