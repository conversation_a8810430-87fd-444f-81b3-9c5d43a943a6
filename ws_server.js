#!/usr/bin/env node
import { WebSocketServer } from 'ws';

// 存储所有连接的客户端
const clients = new Set();

// 创建WebSocket服务器
const wss = new WebSocketServer({
    port: 8766,
    host: '0.0.0.0'
});

console.log('🚀 WebSocket服务器启动成功!');
console.log('📍 监听地址: ws://0.0.0.0:8766');
console.log('⏳ 等待树莓派连接...');

// 处理新连接
wss.on('connection', (ws, req) => {
    const clientIP = req.socket.remoteAddress;
    console.log(`✅ 树莓派已连接: ${clientIP}`);
    
    // 添加到客户端列表
    clients.add(ws);
    console.log(`📊 当前连接数: ${clients.size}`);
    
    // 发送欢迎消息
    try {
        ws.send('连接成功，等待命令...');
        console.log(`📤 欢迎消息已发送到 ${clientIP}`);
    } catch (error) {
        console.error(`❌ 发送欢迎消息失败: ${error.message}`);
    }
    
    // 处理收到的消息
    ws.on('message', (message) => {
        const messageStr = message.toString();
        console.log(`📨 树莓派状态 (${clientIP}): ${messageStr}`);

        // 只记录状态，不发送回复避免循环
        // 如果需要确认，可以在这里添加特定的确认逻辑
    });
    
    // 处理连接关闭
    ws.on('close', (code, reason) => {
        clients.delete(ws);
        console.log(`🔌 树莓派断开连接 (${clientIP}): ${code} ${reason}`);
        console.log(`📊 当前连接数: ${clients.size}`);
    });
    
    // 处理连接错误
    ws.on('error', (error) => {
        console.error(`❌ 连接错误 (${clientIP}): ${error.message}`);
        clients.delete(ws);
    });
    
    // 处理ping/pong
    ws.on('pong', () => {
        console.log(`💓 收到 ${clientIP} 的pong`);
    });
});

// 服务器现在只接收客户端状态，不主动发送命令
// 命令通过 send_command.js 工具发送

// 服务器现在只处理WebSocket连接，不提供命令输入
// 使用 send_command.js 在另一个终端发送命令

// 处理服务器错误
wss.on('error', (error) => {
    console.error(`❌ 服务器错误: ${error.message}`);
});

// 处理进程退出
process.on('SIGINT', () => {
    console.log('\n🛑 收到中断信号，正在关闭服务器...');

    clients.forEach(client => {
        if (client.readyState === 1) {
            client.close(1000, '服务器关闭');
        }
    });

    wss.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

console.log('💬 服务器运行中，使用 send_command.js 发送命令');
console.log('🛑 按 Ctrl+C 停止服务器');

// 定期ping客户端保持连接
setInterval(() => {
    clients.forEach(client => {
        if (client.readyState === 1) {
            client.ping();
        }
    });
}, 30000); // 每30秒ping一次
