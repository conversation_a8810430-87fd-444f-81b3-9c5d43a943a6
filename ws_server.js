#!/usr/bin/env node
import { WebSocketServer } from 'ws';
import { createInterface } from 'readline';

// 存储所有连接的客户端
const clients = new Set();

// 创建WebSocket服务器
const wss = new WebSocketServer({ 
    port: 8765,
    host: '0.0.0.0'
});

console.log('🚀 WebSocket服务器启动成功!');
console.log('📍 监听地址: ws://0.0.0.0:8765');
console.log('⏳ 等待树莓派连接...');

// 处理新连接
wss.on('connection', (ws, req) => {
    const clientIP = req.socket.remoteAddress;
    console.log(`✅ 树莓派已连接: ${clientIP}`);
    
    // 添加到客户端列表
    clients.add(ws);
    console.log(`📊 当前连接数: ${clients.size}`);
    
    // 发送欢迎消息
    try {
        ws.send('连接成功，等待命令...');
        console.log(`📤 欢迎消息已发送到 ${clientIP}`);
    } catch (error) {
        console.error(`❌ 发送欢迎消息失败: ${error.message}`);
    }
    
    // 处理收到的消息
    ws.on('message', (message) => {
        const messageStr = message.toString();
        console.log(`📨 树莓派状态 (${clientIP}): ${messageStr}`);

        // 只记录状态，不发送回复避免循环
        // 如果需要确认，可以在这里添加特定的确认逻辑
    });
    
    // 处理连接关闭
    ws.on('close', (code, reason) => {
        clients.delete(ws);
        console.log(`🔌 树莓派断开连接 (${clientIP}): ${code} ${reason}`);
        console.log(`📊 当前连接数: ${clients.size}`);
    });
    
    // 处理连接错误
    ws.on('error', (error) => {
        console.error(`❌ 连接错误 (${clientIP}): ${error.message}`);
        clients.delete(ws);
    });
    
    // 处理ping/pong
    ws.on('pong', () => {
        console.log(`💓 收到 ${clientIP} 的pong`);
    });
});

// 向所有客户端发送命令
function sendCommand(cmd) {
    if (clients.size === 0) {
        console.log('⚠️  没有活跃的客户端连接');
        return;
    }
    
    console.log(`📤 准备向 ${clients.size} 个客户端发送命令: ${cmd}`);
    
    const disconnectedClients = [];
    
    clients.forEach((client) => {
        if (client.readyState === 1) {
            try {
                client.send(cmd);
                console.log(`✅ 命令已发送到客户端`);
            } catch (error) {
                console.error(`❌ 发送命令失败: ${error.message}`);
                disconnectedClients.push(client);
            }
        } else {
            console.log('🔌 发现断开的连接，将清理');
            disconnectedClients.push(client);
        }
    });
    
    // 清理断开的连接
    disconnectedClients.forEach(client => {
        clients.delete(client);
    });
    
    if (disconnectedClients.length > 0) {
        console.log(`🧹 已清理 ${disconnectedClients.length} 个断开的连接`);
    }
}

// 创建命令行接口
const rl = createInterface({
    input: process.stdin,
    output: process.stdout
});

// 命令输入循环
function promptCommand() {
    rl.question('输入命令 (start_stream/stop_stream/status/exit): ', (cmd) => {
        const command = cmd.trim();
        
        if (command === 'exit') {
            console.log('🛑 正在关闭服务器...');
            
            // 通知所有客户端服务器即将关闭
            sendCommand('server_shutdown');
            
            // 关闭所有连接
            clients.forEach(client => {
                if (client.readyState === 1) {
                    client.close(1000, '服务器关闭');
                }
            });
            
            // 关闭服务器
            wss.close(() => {
                console.log('✅ 服务器已关闭');
                rl.close();
                process.exit(0);
            });
            
            return;
        }
        
        if (['start_stream', 'stop_stream', 'status'].includes(command)) {
            sendCommand(command);
        } else {
            console.log(`⚠️  未知命令: ${command}`);
        }
        
        // 继续等待下一个命令
        promptCommand();
    });
}

// 处理服务器错误
wss.on('error', (error) => {
    console.error(`❌ 服务器错误: ${error.message}`);
});

// 处理进程退出
process.on('SIGINT', () => {
    console.log('\n🛑 收到中断信号，正在关闭服务器...');
    
    clients.forEach(client => {
        if (client.readyState === 1) {
            client.close(1000, '服务器关闭');
        }
    });
    
    wss.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

// 启动命令输入
console.log('💬 命令输入已启动');
promptCommand();

// 定期ping客户端保持连接
setInterval(() => {
    clients.forEach(client => {
        if (client.readyState === 1) {
            client.ping();
        }
    });
}, 30000); // 每30秒ping一次
