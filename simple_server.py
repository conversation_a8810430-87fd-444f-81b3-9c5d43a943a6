#!/usr/bin/env python3
import asyncio
import websockets

clients = set()

async def handler(websocket, path):
    print(f"新连接: {websocket.remote_address}")
    clients.add(websocket)
    
    try:
        await websocket.send("连接成功！")
        async for message in websocket:
            print(f"收到消息: {message}")
            await websocket.send(f"收到: {message}")
    except websockets.exceptions.ConnectionClosed:
        print("连接关闭")
    finally:
        clients.discard(websocket)
        print(f"连接清理完成，剩余连接: {len(clients)}")

async def main():
    print("启动简单WebSocket服务器...")
    server = await websockets.serve(handler, "localhost", 8765)
    print("服务器启动成功，监听 localhost:8765")
    
    # 保持服务器运行
    await server.wait_closed()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n服务器关闭")
