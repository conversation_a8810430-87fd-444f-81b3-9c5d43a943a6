// client.js
import WebSocket from 'ws';

// 修改为你的 Windows IP 和端口
const ws = new WebSocket('ws://************:8765');

ws.on('open', () => {
  console.log('✅ 已连接到 Windows WebSocket 服务');
  ws.send('Hello from Raspberry Pi (Node.js)');
});

ws.on('message', (message) => {
  console.log('📩 收到消息:', message.toString());
});

ws.on('close', () => {
  console.log('❌ 连接关闭');
});

ws.on('error', (err) => {
  console.error('⚠️ 连接错误:', err);
});
