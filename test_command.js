#!/usr/bin/env node
import WebSocket from 'ws';

const WS_SERVER_URI = 'ws://localhost:8766';

function sendTestCommand() {
    console.log('🔗 连接到服务器发送测试命令...');
    
    const ws = new WebSocket(WS_SERVER_URI);
    
    ws.on('open', () => {
        console.log('✅ 连接成功');
        
        // 发送测试命令
        const testCommand = 'status';
        console.log(`📤 发送测试命令: ${testCommand}`);
        ws.send(testCommand);
        
        // 3秒后关闭连接
        setTimeout(() => {
            ws.close();
        }, 3000);
    });
    
    ws.on('message', (data) => {
        const message = data.toString();
        console.log(`📨 收到响应: ${message}`);
    });
    
    ws.on('close', () => {
        console.log('🔌 连接关闭');
        process.exit(0);
    });
    
    ws.on('error', (error) => {
        console.error(`❌ 连接错误: ${error.message}`);
        process.exit(1);
    });
}

sendTestCommand();
