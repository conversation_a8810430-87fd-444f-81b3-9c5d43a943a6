#!/usr/bin/env python3
import asyncio
import websockets

async def test_client():
    uri = "ws://localhost:8767"
    
    try:
        print(f"🔗 正在连接到 {uri}")
        
        async with websockets.connect(uri) as websocket:
            print("✅ 连接成功!")
            
            # 发送测试消息
            test_message = "Hello from client!"
            print(f"📤 发送消息: {test_message}")
            await websocket.send(test_message)
            
            # 接收回复
            response = await websocket.recv()
            print(f"📨 收到回复: {response}")
            
            # 再发送一条消息
            test_message2 = "Second message"
            print(f"📤 发送消息: {test_message2}")
            await websocket.send(test_message2)
            
            response2 = await websocket.recv()
            print(f"📨 收到回复: {response2}")
            
            print("✅ 测试成功!")
            
    except ConnectionRefusedError:
        print("❌ 连接被拒绝 - 请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print(f"错误类型: {type(e).__name__}")

if __name__ == "__main__":
    asyncio.run(test_client())
