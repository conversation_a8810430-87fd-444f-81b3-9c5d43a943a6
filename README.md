# WebSocket 推流控制系统

## 文件说明

- `ws_server.js` - WebSocket服务器（运行在Windows上）
- `test.js` - 树莓派客户端
- `send_command.js` - 命令发送工具（在Windows上使用）

## 使用步骤

### 1. 启动WebSocket服务器（Windows）
```bash
node ws_server.js
```
服务器会监听 `0.0.0.0:8766`，等待树莓派连接。

### 2. 启动树莓派客户端（树莓派）
```bash
node test.js
```
客户端会连接到 `192.168.2.64:8766`。

### 3. 发送命令（Windows，另一个终端）
```bash
node send_command.js
```

## 支持的命令

### 预定义命令：
- `start_stream` - 启动默认推流
- `stop_stream` - 停止推流
- `status` - 查询当前状态
- `exit` - 退出客户端

### 自定义命令：
可以发送任意命令，例如：
```
/usr/bin/rpicam-vid --width 1280 --height 720 --framerate 30 --codec h264 --inline --timeout 0 --libav-format flv --output rtmp://192.168.2.64:1935/live/device_001
```

## 执行流程

1. **Windows端发送命令** → **服务器** → **树莓派客户端**
2. **树莓派收到命令** → **打印指令** → **执行指令** → **发送确认**
3. **服务器收到确认** → **显示执行结果**

## 修复的问题

✅ **消息循环问题**：移除了服务器自动回复，避免无限循环
✅ **推流日志干扰**：推流输出被静默处理，不干扰命令输入
✅ **命令输入分离**：使用独立的命令发送工具
✅ **执行确认**：树莓派执行指令后会发送"已执行指令"确认

## 端口配置

- 服务器端口: `8766`
- 如需修改端口，需要同时修改：
  - `ws_server.js` 中的端口
  - `test.js` 中的连接地址
  - `send_command.js` 中的连接地址
