#!/usr/bin/env python3
import asyncio
import websockets

async def echo_server(websocket, path):
    print(f"✅ 客户端连接成功: {websocket.remote_address}")
    try:
        async for message in websocket:
            print(f"📨 收到消息: {message}")
            await websocket.send(f"回复: {message}")
            print(f"📤 已回复消息")
    except websockets.exceptions.ConnectionClosed:
        print("🔌 连接正常关闭")
    except Exception as e:
        print(f"❌ 连接错误: {e}")

async def main():
    print("🚀 启动WebSocket服务器...")
    
    try:
        # 启动服务器
        server = await websockets.serve(
            echo_server, 
            "localhost", 
            8767,
            ping_interval=None  # 禁用ping以简化调试
        )
        
        print("✅ 服务器启动成功!")
        print("📍 监听地址: ws://localhost:8767")
        print("⏳ 等待客户端连接...")
        print("按 Ctrl+C 停止服务器")
        
        # 保持服务器运行
        await server.wait_closed()
        
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
