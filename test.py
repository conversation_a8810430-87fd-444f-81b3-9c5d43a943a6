import asyncio
import websockets
import subprocess

# 修改为 Windows 的 IP 和端口
WS_SERVER_URI = "ws://************:8765"

async def send_status(ws, status):
    try:
        await ws.send(status)
    except:
        pass

async def handle_commands():
    while True:
        try:
            async with websockets.connect(WS_SERVER_URI) as ws:
                print("已连接到 Windows 控制端")
                while True:
                    cmd = await ws.recv()
                    print("收到命令:", cmd)

                    if cmd == "start_stream":
                        subprocess.Popen(["python3", "pushstream.py"])
                        await send_status(ws, "推流已启动")
                    elif cmd == "stop_stream":
                        subprocess.run(["pkill", "-f", "pushstream.py"])
                        await send_status(ws, "推流已停止")
                    elif cmd == "status":
                        await send_status(ws, "等待推流命令")
                    elif cmd == "exit":
                        await send_status(ws, "客户端退出")
                        return
        except Exception as e:
            print("连接失败，5秒后重试:", e)
            await asyncio.sleep(5)

if __name__ == "__main__":
    asyncio.run(handle_commands())
