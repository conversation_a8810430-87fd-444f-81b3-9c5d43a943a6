import asyncio
import websockets
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_connection():
    uri = "ws://localhost:8765"
    
    try:
        logger.info(f"正在连接到 {uri}")
        
        async with websockets.connect(uri) as websocket:
            logger.info("✅ 连接成功！")
            
            # 等待服务端的欢迎消息
            welcome = await websocket.recv()
            logger.info(f"收到欢迎消息: {welcome}")
            
            # 发送测试消息
            test_message = "Hello from client"
            await websocket.send(test_message)
            logger.info(f"发送消息: {test_message}")
            
            # 接收回复
            response = await websocket.recv()
            logger.info(f"收到回复: {response}")
            
            # 保持连接一段时间
            logger.info("连接测试成功，5秒后断开...")
            await asyncio.sleep(5)
            
    except websockets.exceptions.InvalidMessage as e:
        logger.error(f"❌ WebSocket消息无效: {e}")
        logger.error("这通常表示服务端不是WebSocket服务")
    except ConnectionRefusedError:
        logger.error("❌ 连接被拒绝 - 服务端可能未启动")
    except Exception as e:
        logger.error(f"❌ 连接失败: {e}")
        logger.error(f"错误类型: {type(e)}")

if __name__ == "__main__":
    asyncio.run(test_connection())
